from attr import validate
from fastapi import APIRouter, Depends, status, Query
from sqlalchemy.orm import Session
from controller.member_controller import MemberController
from db.db import get_db
from dependencies.jwt_verifier import verify_user
from dependencies.admin_jwt import validate_jwt_token
from schemas.member import CoMemberCreate, CoMemberUpdate
from typing import List

router = APIRouter(dependencies=[Depends(verify_user)])

memberController = MemberController()

# GET Routes
@router.get("/")
async def get_all_members(
    db: Session = Depends(get_db),
    page: int = Query(1, ge=1),
    pageSize: int = Query(10, ge=1, le=100)
):
    return memberController.get_all_members(db, page=page, pageSize=pageSize)

@router.get("/uuid/{uuid}")
async def get_member_by_uuid(uuid: str, db: Session = Depends(get_db)):
    return memberController.get_member_by_uuid(uuid, db)

@router.get("/auth0/{auth0id}")
async def get_member_by_auth0id(auth0id: str, db: Session = Depends(get_db)):
    return memberController.get_member_by_auth0id(auth0id, db)

@router.get("/loginemail/{email}")
async def get_member_by_email(email: str, db: Session = Depends(get_db)):
    return memberController.get_member_by_email(email, db)

@router.put("/uuid/{uuid}")
async def update_member_by_uuid(uuid: str, member: CoMemberUpdate, db: Session = Depends(get_db), admin_user_payload=Depends(validate_jwt_token)):
    return memberController.update_member_by_uuid(uuid, member, db, admin_user_payload)

@router.delete("/{uuid}")
async def delete_member(uuid: str, db: Session = Depends(get_db), admin_user_payload=Depends(validate_jwt_token)):
    return memberController.delete_member(uuid, db, admin_user_payload)